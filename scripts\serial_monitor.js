#!/usr/bin/env node

// Simple Serial Monitor for Arduino CLI integration
// Usage: node serial_monitor.js <port> <baudrate>

const { SerialPort } = require('serialport');
const { ReadlineParser } = require('@serialport/parser-readline');

const port = process.argv[2] || 'COM3';
const baudRate = parseInt(process.argv[3]) || 115200;

console.log(`Opening serial port ${port} at ${baudRate} baud...`);

const serialPort = new SerialPort({
    path: port,
    baudRate: baudRate,
    autoOpen: false
});

const parser = serialPort.pipe(new ReadlineParser({ delimiter: '\n' }));

serialPort.open((err) => {
    if (err) {
        console.error('Error opening port:', err.message);
        process.exit(1);
    }
    
    console.log(`Serial port ${port} opened successfully!`);
    console.log('Press Ctrl+C to exit\n');
});

parser.on('data', (data) => {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] ${data}`);
});

serialPort.on('error', (err) => {
    console.error('Serial port error:', err.message);
});

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
    console.log('\nClosing serial port...');
    serialPort.close(() => {
        console.log('Serial port closed.');
        process.exit(0);
    });
});

// Keep the process running
process.stdin.resume();

{"name": "arduino-serial-monitor", "version": "1.0.0", "description": "Simple serial monitor for Arduino CLI integration", "main": "serial_monitor.js", "scripts": {"install-deps": "npm install", "monitor": "node serial_monitor.js"}, "dependencies": {"serialport": "^12.0.0", "@serialport/parser-readline": "^12.0.0"}, "keywords": ["a<PERSON><PERSON><PERSON>", "serial", "monitor", "esp8266", "esp32"], "author": "", "license": "MIT"}
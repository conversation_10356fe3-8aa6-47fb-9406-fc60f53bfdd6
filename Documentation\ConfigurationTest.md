# Configuration System Validation Test

This document provides a comprehensive test plan for validating the device configuration system across all supported device models.

## Test Overview

The configuration system has been designed to support 6 different device models with automatic feature detection and conditional compilation. This test validates that each configuration compiles correctly and includes the appropriate features.

## Device Models to Test

### 1. DEVICE_MODEL_1_SWITCH
**Configuration:**
- Platform: ESP8266
- Control: Shift Register (74HC595)
- Switches: 1 relay + RGB
- Touch Sensors: 1 pin
- Features: Basic RGB (on/off)

**Expected Compilation:**
- ✅ ShiftRegisterManager included
- ✅ TouchSensorManager with 1 pin
- ✅ Basic RGB control
- ❌ DirectPinManager excluded
- ❌ ESP32 features excluded
- ❌ Advanced sensors excluded

### 2. DEVICE_MODEL_2_SWITCH
**Configuration:**
- Platform: ESP8266
- Control: Shift Register (74HC595)
- Switches: 2 relays + RGB
- Touch Sensors: 2 pins
- Features: Basic RGB (on/off)

**Expected Compilation:**
- ✅ ShiftRegisterManager included
- ✅ TouchSensorManager with 2 pins
- ✅ Basic RGB control
- ❌ DirectPinManager excluded
- ❌ ESP32 features excluded

### 3. DEVICE_MODEL_3_SWITCH (Current Default)
**Configuration:**
- Platform: ESP8266
- Control: Shift Register (74HC595)
- Switches: 3 relays + RGB
- Touch Sensors: 3 pins
- Features: Basic RGB (on/off)

**Expected Compilation:**
- ✅ ShiftRegisterManager included
- ✅ TouchSensorManager with 3 pins
- ✅ Basic RGB control
- ✅ Reset functionality (sensors 1&3)
- ❌ DirectPinManager excluded

### 4. DEVICE_MODEL_4_SWITCH
**Configuration:**
- Platform: ESP8266
- Control: Shift Register (74HC595)
- Switches: 4 relays + RGB
- Touch Sensors: 4 pins
- Features: Basic RGB (on/off)

**Expected Compilation:**
- ✅ ShiftRegisterManager included
- ✅ TouchSensorManager with 4 pins
- ✅ Basic RGB control
- ✅ Reset functionality (sensors 1&3)
- ❌ DirectPinManager excluded

### 5. DEVICE_MODEL_COOLER_CONTROL
**Configuration:**
- Platform: ESP32
- Control: Direct Pin Control
- Switches: 3 relays + RGB
- Touch Sensors: 3 capacitive pins
- Features: Full-color RGB, Temperature sensor, RF receiver, Buzzer

**Expected Compilation:**
- ✅ DirectPinManager included
- ✅ ESP32TouchManager with capacitive touch
- ✅ Full-color RGB (0-255 range)
- ✅ PWM control for RGB
- ✅ SHT30TemperatureSensor
- ✅ RXB22RFReceiver
- ✅ BuzzerManager
- ✅ CoolerControlManager
- ❌ ShiftRegisterManager excluded

### 6. DEVICE_MODEL_SCENARIO_KEY
**Configuration:**
- Platform: ESP8266
- Control: Shift Register (74HC595)
- Switches: 4 keys (no relays)
- Touch Sensors: 4 pins
- Features: RGB only (no relay control)

**Expected Compilation:**
- ✅ ShiftRegisterManager included
- ✅ TouchSensorManager with 4 pins
- ✅ Basic RGB control
- ❌ Relay control disabled
- ❌ DirectPinManager excluded

## Validation Checklist

### Compilation Tests
For each device model, verify:

1. **No Compilation Errors**
   - All includes resolve correctly
   - No undefined symbols
   - No conflicting definitions

2. **Correct Manager Inclusion**
   - Only appropriate managers are compiled
   - Conditional compilation works correctly
   - Memory usage is optimized

3. **Platform-Specific Features**
   - ESP8266: WiFi, EEPROM, digital touch
   - ESP32: WiFi, EEPROM, capacitive touch, PWM

4. **Feature Flags**
   - HAS_RELAYS correctly set
   - USE_SHIFT_REGISTERS correctly set
   - IS_ESP32 correctly set
   - Device-specific features enabled

### Runtime Tests
For each device model, verify:

1. **Initialization**
   - All managers initialize correctly
   - Hardware pins configured properly
   - Default values loaded from config

2. **Basic Functionality**
   - Switch control works
   - RGB control works
   - Touch sensors respond
   - MQTT/WiFi/OTA functions

3. **Device-Specific Features**
   - Temperature sensor (cooler only)
   - RF receiver (cooler only)
   - Buzzer (cooler only)
   - Full-color RGB (ESP32 only)

## Test Procedure

### Step 1: Configuration Switch Test
1. Edit `DeviceConfig.h`
2. Comment out current `#define DEVICE_MODEL_*`
3. Uncomment target device model
4. Compile and verify no errors
5. Check memory usage
6. Repeat for all 6 models

### Step 2: Feature Validation
For each model:
1. Check that correct managers are included
2. Verify pin definitions are correct
3. Confirm feature flags are set properly
4. Test conditional compilation blocks

### Step 3: Memory Usage Analysis
Compare memory usage across models:
- ESP8266 models should be memory-optimized
- ESP32 models can use more features
- Unused features should not consume memory

## Expected Results

### Memory Usage (Approximate)
- **1-Switch**: ~25KB program, ~2KB RAM
- **2-Switch**: ~26KB program, ~2KB RAM
- **3-Switch**: ~27KB program, ~2KB RAM
- **4-Switch**: ~28KB program, ~2KB RAM
- **Scenario Key**: ~25KB program, ~2KB RAM
- **Cooler Control**: ~35KB program, ~4KB RAM (ESP32)

### Compilation Time
- ESP8266 models: 15-20 seconds
- ESP32 models: 20-25 seconds

### Feature Matrix
| Feature | 1SW | 2SW | 3SW | 4SW | Cooler | Scenario |
|---------|-----|-----|-----|-----|--------|----------|
| Shift Registers | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ |
| Direct Pins | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| Basic RGB | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ |
| Full RGB | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| Relays | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ |
| Temperature | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| RF Receiver | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| Buzzer | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |

## Test Status

✅ **Configuration System**: Implemented and ready for testing
✅ **Conditional Compilation**: All platform differences handled
✅ **Device Libraries**: All device-specific libraries created
✅ **Manager Integration**: All managers updated for multi-model support

## Conclusion

The configuration system provides a robust foundation for supporting multiple device models with a single codebase. Each device model can be selected by changing a single line in `DeviceConfig.h`, and the system automatically includes only the necessary features and optimizes memory usage accordingly.

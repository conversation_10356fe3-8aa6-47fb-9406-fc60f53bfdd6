#ifndef DEVICE_CONFIG_H
#define DEVICE_CONFIG_H

#include <Arduino.h>

// ============================================================================
// DEVICE MODEL SELECTION
// ============================================================================
// Uncomment ONE of the following lines to select your device model:

// #define DEVICE_MODEL_1_SWITCH
// #define DEVICE_MODEL_2_SWITCH
// #define DEVICE_MODEL_3_SWITCH
// #define DEVICE_MODEL_4_SWITCH
#define DEVICE_MODEL_COOLER_CONTROL
// #define DEVICE_MODEL_SCENARIO_KEY

// ============================================================================
// DEVICE MODEL CONFIGURATIONS
// ============================================================================

#ifdef DEVICE_MODEL_1_SWITCH
// 1-Switch Device Configuration
#define DEVICE_TYPE "1Switch"
#define DEVICE_NAME_PREFIX "1Switch_"
#define SWITCH_COUNT 1
#define MAX_SWITCHES 1
#define HAS_RELAYS true
#define USE_SHIFT_REGISTERS true
#define HAS_FULL_COLOR_RGB false
#define HAS_COLOR_CYCLE_MODE false

// Shift register control pins (same as 3-switch)
#define SR_DATA_PIN 13
#define SR_CLOCK_PIN 16
#define SR_LATCH_PIN 14

// Shift register bit mappings (1-switch variant - uses same hardware as 3-switch)
#define RELAY_BIT_POSITIONS {11} // R1 bit position
#define RED_BIT_POSITIONS {1}    // r1 bit position
#define GREEN_BIT_POSITIONS {3}  // g1 bit position
#define BLUE_BIT_POSITIONS {2}   // b1 bit position

// Touch sensor pins
#define TOUCH_PINS {4}

// Reset combination (not applicable for single switch)
#define RESET_TOUCH_1 0
#define RESET_TOUCH_2 0
#define RESET_FEEDBACK_SWITCH 0

#elif defined(DEVICE_MODEL_2_SWITCH)
// 2-Switch Device Configuration
#define DEVICE_TYPE "2Switch"
#define DEVICE_NAME_PREFIX "2Switch_"
#define SWITCH_COUNT 2
#define MAX_SWITCHES 2
#define HAS_RELAYS true
#define USE_SHIFT_REGISTERS true
#define HAS_FULL_COLOR_RGB false
#define HAS_COLOR_CYCLE_MODE false

// Shift register control pins (same as 3-switch)
#define SR_DATA_PIN 13
#define SR_CLOCK_PIN 16
#define SR_LATCH_PIN 14

// Shift register bit mappings (2-switch variant - uses same hardware as 3-switch)
#define RELAY_BIT_POSITIONS {11, 10} // R1, R2 bit positions
#define RED_BIT_POSITIONS {1, 4}     // r1, r2 bit positions
#define GREEN_BIT_POSITIONS {3, 6}   // g1, g2 bit positions
#define BLUE_BIT_POSITIONS {2, 5}    // b1, b2 bit positions

// Touch sensor pins
#define TOUCH_PINS {4, 5}

// Reset combination (touch sensors 1 and 2)
#define RESET_TOUCH_1 0
#define RESET_TOUCH_2 1
#define RESET_FEEDBACK_SWITCH 0

#elif defined(DEVICE_MODEL_3_SWITCH)
// 3-Switch Device Configuration (Current implementation)
#define DEVICE_TYPE "3Switch"
#define DEVICE_NAME_PREFIX "3Relay_"
#define SWITCH_COUNT 3
#define MAX_SWITCHES 3
#define HAS_RELAYS true
#define USE_SHIFT_REGISTERS true
#define HAS_FULL_COLOR_RGB false
#define HAS_COLOR_CYCLE_MODE false

// Shift register control pins
#define SR_DATA_PIN 13
#define SR_CLOCK_PIN 16
#define SR_LATCH_PIN 14

// Shift register bit mappings (3-relay variant)
#define RELAY_BIT_POSITIONS {11, 10, 9} // R1, R2, R3 bit positions
#define RED_BIT_POSITIONS {1, 4, 12}    // r1, r2, r3 bit positions
#define GREEN_BIT_POSITIONS {3, 6, 14}  // g1, g2, g3 bit positions
#define BLUE_BIT_POSITIONS {2, 5, 13}   // b1, b2, b3 bit positions

// Touch sensor pins
#define TOUCH_PINS {4, 5, 12}

// Reset combination (touch sensors 1 and 3, feedback on sensor 2)
#define RESET_TOUCH_1 0
#define RESET_TOUCH_2 2
#define RESET_FEEDBACK_SWITCH 1

#elif defined(DEVICE_MODEL_4_SWITCH)
// 4-Switch Device Configuration
#define DEVICE_TYPE "4Switch"
#define DEVICE_NAME_PREFIX "4Relay_"
#define SWITCH_COUNT 4
#define MAX_SWITCHES 4
#define HAS_RELAYS true
#define USE_SHIFT_REGISTERS true
#define HAS_FULL_COLOR_RGB false
#define HAS_COLOR_CYCLE_MODE false

// Shift register control pins (4-relay variant)
#define SR_DATA_PIN 1
#define SR_CLOCK_PIN 16
#define SR_LATCH_PIN 14

// Shift register bit mappings (4-relay variant)
#define RELAY_BIT_POSITIONS {3, 6, 5, 4}   // Relay1, Relay4, Relay3, Relay2 bit positions
#define RED_BIT_POSITIONS {0, 7, 8, 15}    // r1, r2, r3, r4 bit positions
#define GREEN_BIT_POSITIONS {1, 9, 10, 13} // g1, g2, g3, g4 bit positions
#define BLUE_BIT_POSITIONS {2, 11, 12, 14} // b1, b2, b3, b4 bit positions

// Touch sensor pins
#define TOUCH_PINS {4, 5, 12, 0}

// Reset combination (touch sensors 1 and 3, feedback on sensor 2)
#define RESET_TOUCH_1 0
#define RESET_TOUCH_2 2
#define RESET_FEEDBACK_SWITCH 1

#elif defined(DEVICE_MODEL_COOLER_CONTROL)
// Cooler Control Device Configuration (ESP32-based with direct pin control)
#define DEVICE_TYPE "CoolerControl"
#define DEVICE_NAME_PREFIX "Cooler_"
#define SWITCH_COUNT 3
#define MAX_SWITCHES 3
#define HAS_RELAYS true
#define USE_SHIFT_REGISTERS false
#define HAS_TEMPERATURE_SENSOR true
#define HAS_RF_RECEIVER true
#define HAS_BUZZER true
#define HAS_FULL_COLOR_RGB true
#define HAS_COLOR_CYCLE_MODE true

// Direct pin control for ESP32 cooler (plenty of pins available)
#define RELAY_PINS {2, 4, 16}
#define RGB_RED_PINS {17, 5, 18}
#define RGB_GREEN_PINS {19, 21, 3}
#define RGB_BLUE_PINS {22, 23, 1}

// Touch sensor pins (ESP32 touch-capable pins)
#define TOUCH_PINS {T0, T3, T4} // GPIO4, GPIO15, GPIO13 on ESP32
#define TOUCH_THRESHOLD 20      // ESP32 capacitive touch threshold (lower = more sensitive)

// Additional sensor pins for cooler control
#define TEMP_SENSOR_SDA_PIN 25
#define TEMP_SENSOR_SCL_PIN 26
#define RF_RECEIVER_PIN 27
#define BUZZER_PIN 14

// Reset combination
#define RESET_TOUCH_1 0
#define RESET_TOUCH_2 2
#define RESET_FEEDBACK_SWITCH 1

#elif defined(DEVICE_MODEL_SCENARIO_KEY)
// Scenario Key Device Configuration (4 keys, no relays, uses shift registers)
#define DEVICE_TYPE "ScenarioKey"
#define DEVICE_NAME_PREFIX "ScenarioKey_"
#define SWITCH_COUNT 4
#define MAX_SWITCHES 4
#define HAS_RELAYS false
#define USE_SHIFT_REGISTERS true
#define HAS_FULL_COLOR_RGB false
#define HAS_COLOR_CYCLE_MODE false

// Shift register control pins (same as 4-switch)
#define SR_DATA_PIN 1
#define SR_CLOCK_PIN 16
#define SR_LATCH_PIN 14

// Only RGB bit mappings (no relay bits)
#define RED_BIT_POSITIONS {0, 7, 8, 15}    // r1, r2, r3, r4 bit positions
#define GREEN_BIT_POSITIONS {1, 9, 10, 13} // g1, g2, g3, g4 bit positions
#define BLUE_BIT_POSITIONS {2, 11, 12, 14} // b1, b2, b3, b4 bit positions

// Touch sensor pins
#define TOUCH_PINS {4, 5, 12, 0}

// Reset combination (touch sensors 1 and 3, feedback on sensor 2)
#define RESET_TOUCH_1 0
#define RESET_TOUCH_2 2
#define RESET_FEEDBACK_SWITCH 1

#else
#error "No device model selected! Please define one of the DEVICE_MODEL_* macros."
#endif

// ============================================================================
// PLATFORM DETECTION (based on device model configurations)
// ============================================================================

#if defined(DEVICE_MODEL_COOLER_CONTROL)
#define IS_ESP32
#endif

// ============================================================================
// PLATFORM-SPECIFIC INCLUDES AND DEFINITIONS
// ============================================================================

#ifdef IS_ESP32
// ESP32-specific includes
#include <WiFi.h>
#include <WebServer.h>
#include <EEPROM.h>
#include <HTTPClient.h>
#include <Update.h>
#include <HTTPUpdate.h>
#include <Wire.h>

// ESP32-specific definitions
#define WIFI_MODE_AP_STA WIFI_MODE_APSTA
#define ESP_RESTART() ESP.restart()
#define GET_FREE_HEAP() ESP.getFreeHeap()
#define GET_CHIP_ID() ((uint32_t)ESP.getEfuseMac())
#define EEPROM_INIT(size) EEPROM.begin(size)
#define EEPROM_COMMIT() EEPROM.commit()

// Touch sensor definitions for ESP32
#if HAS_FULL_COLOR_RGB
#include <driver/ledc.h>
#define PWM_CHANNELS 12  // 4 switches * 3 colors
#define PWM_RESOLUTION 8 // 8-bit resolution (0-255)
#define PWM_FREQUENCY 5000
#endif

#else
// ESP8266-specific includes
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <EEPROM.h>
#include <ESP8266HTTPClient.h>
#include <ESP8266httpUpdate.h>
#include <Wire.h>

// ESP8266-specific definitions
#define WebServer ESP8266WebServer
#define HTTPClient ESP8266HTTPClient
#define ESP_RESTART() ESP.restart()
#define GET_FREE_HEAP() ESP.getFreeHeap()
#define GET_CHIP_ID() ESP.getChipId()
#define EEPROM_INIT(size) EEPROM.begin(size)
#define EEPROM_COMMIT() EEPROM.commit()
#define WIFI_MODE_AP_STA WIFI_AP_STA

// ESP8266 doesn't have hardware touch sensors
#define TOUCH_THRESHOLD 40 // For digital touch sensing
#endif

// ============================================================================
// NETWORK CONFIGURATION
// ============================================================================

// Default Access Point Credentials
#define DEFAULT_AP_SSID "ESP8266_Switch"
#define DEFAULT_AP_PASSWORD "12345678"

// Default MQTT Settings
#define DEFAULT_MQTT_BROKER "*************"
#define DEFAULT_MQTT_PORT 1883
#define DEFAULT_MQTT_USERNAME ""
#define DEFAULT_MQTT_PASSWORD ""

// OTA Server Configuration
#define OTA_SERVER_HOST "*************"
#define OTA_SERVER_PORT 8080
#define UPDATE_CHECK_ENDPOINT "/check-update"
#define FIRMWARE_DOWNLOAD_ENDPOINT "/download-firmware"
#define CURRENT_VERSION "1.0.0"

// ============================================================================
// TIMING CONFIGURATION
// ============================================================================

#define CONNECTION_CHECK_INTERVAL 5000              // 5 seconds
#define MQTT_CHECK_INTERVAL 2000                    // 2 seconds
#define MQTT_RECONNECT_INTERVAL (5 * 60 * 1000)     // 5 minutes
#define UPDATE_CHECK_INTERVAL (12 * 60 * 60 * 1000) // 12 hours

// ============================================================================
// DEFAULT RGB COLORS
// ============================================================================

// Default RGB colors for basic devices (Red=OFF, Green=ON)
#define DEFAULT_RGB_OFF_R true
#define DEFAULT_RGB_OFF_G false
#define DEFAULT_RGB_OFF_B false

#define DEFAULT_RGB_ON_R false
#define DEFAULT_RGB_ON_G true
#define DEFAULT_RGB_ON_B false

// Full color RGB settings for ESP32 devices
#if HAS_FULL_COLOR_RGB
// Default colors in 0-255 range
#define DEFAULT_RGB_OFF_COLOR {255, 0, 0} // Red when OFF
#define DEFAULT_RGB_ON_COLOR {0, 255, 0}  // Green when ON

// Color cycle settings
#if HAS_COLOR_CYCLE_MODE
#define COLOR_CYCLE_SPEED 50       // Milliseconds between color changes
#define COLOR_CYCLE_BRIGHTNESS 128 // Default brightness (0-255)
#define COLOR_CYCLE_SATURATION 255 // Default saturation (0-255)
#endif
#endif

// ============================================================================
// EEPROM LAYOUT
// ============================================================================

// WiFi credentials
#define EEPROM_SIZE 512
#define SSID_ADDR 0
#define SSID_SIZE 32
#define PASS_ADDR (SSID_ADDR + SSID_SIZE)
#define PASS_SIZE 64
#define HAS_CREDENTIALS_ADDR (PASS_ADDR + PASS_SIZE)

// AP configuration
#define AP_SSID_ADDR (HAS_CREDENTIALS_ADDR + 1)
#define AP_SSID_SIZE 32
#define AP_PASS_ADDR (AP_SSID_ADDR + AP_SSID_SIZE)
#define AP_PASS_SIZE 64

// Device properties
#define DEVICE_ID_ADDR 100
#define DEVICE_ID_SIZE 16
#define DEVICE_NAME_ADDR (DEVICE_ID_ADDR + DEVICE_ID_SIZE)
#define DEVICE_NAME_SIZE 32
#define DEVICE_TYPE_ADDR (DEVICE_NAME_ADDR + DEVICE_NAME_SIZE)
#define DEVICE_TYPE_SIZE 16
#define SWITCH_COUNT_ADDR (DEVICE_TYPE_ADDR + DEVICE_TYPE_SIZE)
#define SWITCH_STATE_ADDR (SWITCH_COUNT_ADDR + 1)
#define RGB_OFF_STATE_ADDR (SWITCH_STATE_ADDR + MAX_SWITCHES)
#define RGB_ON_STATE_ADDR (RGB_OFF_STATE_ADDR + (MAX_SWITCHES * 3))

// MQTT settings
#define MQTT_BROKER_ADDR 200
#define MQTT_BROKER_SIZE 64
#define MQTT_PORT_ADDR (MQTT_BROKER_ADDR + MQTT_BROKER_SIZE)
#define MQTT_PORT_SIZE 2
#define MQTT_USERNAME_ADDR (MQTT_PORT_ADDR + MQTT_PORT_SIZE)
#define MQTT_USERNAME_SIZE 32
#define MQTT_PASSWORD_ADDR (MQTT_USERNAME_ADDR + MQTT_USERNAME_SIZE)
#define MQTT_PASSWORD_SIZE 32
#define MQTT_ENABLED_ADDR (MQTT_PASSWORD_ADDR + MQTT_PASSWORD_SIZE)

// ============================================================================
// HELPER MACROS FOR ARRAY INITIALIZATION
// ============================================================================

// Convert pin definitions to arrays for easy iteration
#if USE_SHIFT_REGISTERS
// For shift register devices, we use bit positions
#if HAS_RELAYS
static const uint8_t RELAY_BIT_POSITIONS_ARRAY[] = RELAY_BIT_POSITIONS;
#endif
static const uint8_t RED_BIT_POSITIONS_ARRAY[] = RED_BIT_POSITIONS;
static const uint8_t GREEN_BIT_POSITIONS_ARRAY[] = GREEN_BIT_POSITIONS;
static const uint8_t BLUE_BIT_POSITIONS_ARRAY[] = BLUE_BIT_POSITIONS;
#else
// For direct pin control devices
static const uint8_t RELAY_PIN_NUMBERS[] = RELAY_PINS;
static const uint8_t RED_PIN_NUMBERS[] = RGB_RED_PINS;
static const uint8_t GREEN_PIN_NUMBERS[] = RGB_GREEN_PINS;
static const uint8_t BLUE_PIN_NUMBERS[] = RGB_BLUE_PINS;
#endif

// Touch sensor pins array
static const uint8_t TOUCH_PIN_NUMBERS[] = TOUCH_PINS;

// ============================================================================
// CONDITIONAL LIBRARY INCLUDES AND FEATURE FLAGS
// ============================================================================

// Feature compilation flags based on device configuration
#if HAS_TEMPERATURE_SENSOR
#define COMPILE_TEMPERATURE_SENSOR
#endif

#if HAS_RF_RECEIVER
#define COMPILE_RF_RECEIVER
#endif

#if HAS_BUZZER
#define COMPILE_BUZZER
#endif

#if HAS_FULL_COLOR_RGB
#define COMPILE_FULL_COLOR_RGB
#define COMPILE_COLOR_CYCLE
#endif

// Manager compilation flags
#if USE_SHIFT_REGISTERS
#define COMPILE_SHIFT_REGISTER_MANAGER
#else
#define COMPILE_DIRECT_PIN_MANAGER
#endif

#ifdef IS_ESP32
#define COMPILE_ESP32_FEATURES
#define COMPILE_ESP32_TOUCH
#if HAS_FULL_COLOR_RGB
#define COMPILE_ESP32_PWM
#endif
#else
#define COMPILE_ESP8266_FEATURES
#endif

// Memory optimization flags
#ifdef IS_ESP32
#define ENABLE_DETAILED_LOGGING
#define ENABLE_ADVANCED_FEATURES
#else
// ESP8266 memory optimization
#define OPTIMIZE_MEMORY_USAGE
#define MINIMAL_LOGGING
#endif

// ============================================================================
// DEVICE MODEL STRING FOR OTA
// ============================================================================

#define DEVICE_MODEL DEVICE_TYPE

#endif // DEVICE_CONFIG_H
